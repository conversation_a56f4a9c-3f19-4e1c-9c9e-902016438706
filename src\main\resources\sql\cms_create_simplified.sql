/*
 * 合同验收系统简化版数据库表创建脚本
 * 基于PostgreSQL数据库
 * 简化业务流程：上传资料+填写信息=验收通过
 * 创建时间: 2025-01-16
 */

-- ==============================================
-- 合同验收系统简化表结构
-- ==============================================

-- ----------------------------
-- 1. 验收记录主表 - cms_acceptance_record (简化版)
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_acceptance_record";
CREATE TABLE "public"."cms_acceptance_record" (
  "id" SERIAL PRIMARY KEY,
  "record_no" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "contract_id" int4 NOT NULL,
  "contract_code" varchar(100) COLLATE "pg_catalog"."default",
  "contract_name" varchar(200) COLLATE "pg_catalog"."default",
  
  -- 验收基本信息
  "acceptance_date" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "acceptance_person" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "acceptance_person_name" varchar(50) COLLATE "pg_catalog"."default",
  "acceptance_dept" varchar(50) COLLATE "pg_catalog"."default",
  "acceptance_dept_name" varchar(100) COLLATE "pg_catalog"."default",
  
  -- 验收说明
  "acceptance_desc" text COLLATE "pg_catalog"."default",
  
  -- 验收结果（固定为通过，记录即通过）
  "acceptance_result" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'PASS',
  
  -- 系统字段
  "hospital_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "creator" varchar(50) COLLATE "pg_catalog"."default",
  "update_time" varchar(50) COLLATE "pg_catalog"."default",
  "updater" varchar(50) COLLATE "pg_catalog"."default",
  "is_deleted" int2 DEFAULT 0
);

-- 字段注释
COMMENT ON COLUMN "public"."cms_acceptance_record"."id" IS '主键ID';
COMMENT ON COLUMN "public"."cms_acceptance_record"."record_no" IS '验收记录编号';
COMMENT ON COLUMN "public"."cms_acceptance_record"."contract_id" IS '合同ID';
COMMENT ON COLUMN "public"."cms_acceptance_record"."contract_code" IS '合同编号';
COMMENT ON COLUMN "public"."cms_acceptance_record"."contract_name" IS '合同名称';
COMMENT ON COLUMN "public"."cms_acceptance_record"."acceptance_date" IS '验收日期';
COMMENT ON COLUMN "public"."cms_acceptance_record"."acceptance_person" IS '验收人ID';
COMMENT ON COLUMN "public"."cms_acceptance_record"."acceptance_person_name" IS '验收人姓名';
COMMENT ON COLUMN "public"."cms_acceptance_record"."acceptance_dept" IS '验收科室ID';
COMMENT ON COLUMN "public"."cms_acceptance_record"."acceptance_dept_name" IS '验收科室名称';
COMMENT ON COLUMN "public"."cms_acceptance_record"."acceptance_desc" IS '验收说明';
COMMENT ON COLUMN "public"."cms_acceptance_record"."acceptance_result" IS '验收结果：PASS-通过（固定值）';
COMMENT ON COLUMN "public"."cms_acceptance_record"."hospital_id" IS '医院ID';
COMMENT ON COLUMN "public"."cms_acceptance_record"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cms_acceptance_record"."creator" IS '创建人';
COMMENT ON COLUMN "public"."cms_acceptance_record"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."cms_acceptance_record"."updater" IS '更新人';
COMMENT ON COLUMN "public"."cms_acceptance_record"."is_deleted" IS '逻辑删除标识：0-未删除,1-已删除';
COMMENT ON TABLE "public"."cms_acceptance_record" IS '合同验收记录主表（简化版）';

-- 主键和索引
DROP INDEX IF EXISTS "public"."uk_record_no";
CREATE UNIQUE INDEX "uk_record_no" ON "public"."cms_acceptance_record" USING btree ("record_no");
DROP INDEX IF EXISTS "public"."idx_record_contract_id";
CREATE INDEX "idx_record_contract_id" ON "public"."cms_acceptance_record" USING btree ("contract_id");
DROP INDEX IF EXISTS "public"."idx_acceptance_date";
CREATE INDEX "idx_acceptance_date" ON "public"."cms_acceptance_record" USING btree ("acceptance_date");

-- ----------------------------
-- 2. 验收附件表 - cms_acceptance_attachment (保持不变)
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_acceptance_attachment";
CREATE TABLE "public"."cms_acceptance_attachment" (
  "id" SERIAL PRIMARY KEY,
  "record_id" int4 NOT NULL,
  "file_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "file_path" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
  "file_size" int8,
  "file_ext" varchar(10) COLLATE "pg_catalog"."default",
  "bucket" varchar(50) COLLATE "pg_catalog"."default" DEFAULT 'cms',
  "upload_person" varchar(50) COLLATE "pg_catalog"."default",
  "upload_person_name" varchar(50) COLLATE "pg_catalog"."default",
  "upload_time" varchar(50) COLLATE "pg_catalog"."default",
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  
  -- 系统字段
  "hospital_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "creator" varchar(50) COLLATE "pg_catalog"."default",
  "is_deleted" int2 DEFAULT 0
);

-- 字段注释
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."id" IS '主键ID';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."record_id" IS '验收记录ID';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."file_name" IS '文件名称';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."file_path" IS '文件路径';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."file_size" IS '文件大小（字节）';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."file_ext" IS '文件扩展名';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."bucket" IS '存储桶';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."upload_person" IS '上传人ID';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."upload_person_name" IS '上传人姓名';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."upload_time" IS '上传时间';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."remark" IS '备注';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."hospital_id" IS '医院ID';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."creator" IS '创建人';
COMMENT ON COLUMN "public"."cms_acceptance_attachment"."is_deleted" IS '逻辑删除标识：0-未删除,1-已删除';
COMMENT ON TABLE "public"."cms_acceptance_attachment" IS '验收附件表';

-- 主键和索引
DROP INDEX IF EXISTS "public"."idx_attachment_record_id";
CREATE INDEX "idx_attachment_record_id" ON "public"."cms_acceptance_attachment" USING btree ("record_id");

-- ==============================================
-- 现有表扩展字段
-- ==============================================

-- ----------------------------
-- 扩展合同主表添加验收状态字段
-- ----------------------------
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cms_contract' 
        AND column_name = 'acceptance_status'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "public"."cms_contract" ADD COLUMN "acceptance_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'PENDING';
        COMMENT ON COLUMN "public"."cms_contract"."acceptance_status" IS '验收状态：PENDING-待验收,PASSED-验收通过';
    END IF;
END $$;

-- ==============================================
-- 创建完成提示
-- ==============================================

DO $$
BEGIN
    RAISE NOTICE '✅ 合同验收系统简化版数据库表创建完成！';
    RAISE NOTICE '📋 已创建2张验收相关表：';
    RAISE NOTICE '   1. cms_acceptance_record - 验收记录主表（简化版）';
    RAISE NOTICE '   2. cms_acceptance_attachment - 验收附件表';
    RAISE NOTICE '🔧 已扩展现有表字段：';
    RAISE NOTICE '   - cms_contract.acceptance_status - 验收状态';
    RAISE NOTICE '🚀 简化版验收系统表结构部署完成！';
    RAISE NOTICE '💡 业务逻辑：上传资料+填写信息=验收通过';
END $$;
